"""
Vue模板渲染器
负责Vue单文件组件的渲染和HTML生成
"""
import os
import json
import logging
from django.conf import settings
from django.template.loader import get_template
from django.template.exceptions import TemplateDoesNotExist
from django.utils.safestring import mark_safe

from .vue_compiler import VueCompiler
from .vue_cache import VueTemplateCache

logger = logging.getLogger(__name__)


class VueTemplateRenderer:
    """Vue模板渲染器"""
    
    def __init__(self):
        self.compiler = VueCompiler()
        self.cache = VueTemplateCache()
        
    def render(self, request, template_name, context=None):
        """
        渲染Vue模板
        
        Args:
            request: Django请求对象
            template_name: Vue模板文件名（如 'pages/product.vue'）
            context: 模板上下文数据
            
        Returns:
            str: 渲染后的HTML内容
        """
        try:
            # 获取Vue模板文件路径
            template_path = self._get_template_path(template_name)
            
            # 检查模板文件是否存在
            if not os.path.exists(template_path):
                raise TemplateDoesNotExist(f"Vue模板不存在: {template_name}")
            
            # 尝试从缓存获取编译结果
            cached_result = self.cache.get_cached_template(template_path)
            if cached_result:
                return self._render_with_context(cached_result, context)
            
            # 编译Vue模板
            compiled_result = self.compiler.compile_vue_file(template_path, context)
            
            # 缓存编译结果
            self.cache.cache_template(template_path, compiled_result)
            
            # 渲染最终HTML
            return self._render_with_context(compiled_result, context)
            
        except Exception as e:
            logger.error(f"Vue模板渲染失败: {template_name} - {str(e)}")
            raise
    
    def _get_template_path(self, template_name):
        """获取Vue模板文件的完整路径"""
        vue_dir = getattr(settings, 'VUE_SETTINGS', {}).get('TEMPLATES', {}).get('VUE_DIR')
        if not vue_dir:
            vue_dir = os.path.join(settings.BASE_DIR, 'templates', 'vue')
        
        return os.path.join(vue_dir, template_name)
    
    def _render_with_context(self, compiled_result, context):
        """
        将编译后的Vue组件与Django context结合渲染
        
        Args:
            compiled_result: Vue编译结果
            context: Django上下文数据
            
        Returns:
            str: 最终的HTML内容
        """
        try:
            # 获取编译后的HTML模板和JavaScript代码
            html_template = compiled_result.get('html', '')
            javascript_code = compiled_result.get('javascript', '')
            css_code = compiled_result.get('css', '')
            
            # 序列化context数据为JSON
            context_json = json.dumps(context or {}, ensure_ascii=False, default=str)
            
            # 构建最终的HTML页面
            final_html = self._build_final_html(
                html_template=html_template,
                javascript_code=javascript_code,
                css_code=css_code,
                context_json=context_json
            )
            
            return final_html
            
        except Exception as e:
            logger.error(f"Vue模板上下文渲染失败: {str(e)}")
            raise
    
    def _build_final_html(self, html_template, javascript_code, css_code, context_json):
        """
        构建最终的HTML页面
        
        Args:
            html_template: HTML模板内容
            javascript_code: JavaScript代码
            css_code: CSS样式代码
            context_json: 序列化的context数据
            
        Returns:
            str: 完整的HTML页面
        """
        # 基础HTML结构
        base_html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue页面</title>
    
    <!-- Vue运行时 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

    <!-- MD5库 - 改进的多源加载策略 -->
    <script>
        // 改进的MD5库加载器，支持多个CDN源和本地后备
        (function() {{
            console.log('[MD5 Loader] 开始加载MD5库');
            
            // MD5库加载状态管理
            window.__MD5_LOADER__ = {{
                loaded: false,
                loading: false,
                error: null,
                callbacks: []
            }};
            
            // 注册MD5库就绪回调
            window.onMD5Ready = function(callback) {{
                if (window.__MD5_LOADER__.loaded) {{
                    callback();
                }} else {{
                    window.__MD5_LOADER__.callbacks.push(callback);
                }}
            }};
            
            // 触发所有等待的回调
            function triggerCallbacks() {{
                window.__MD5_LOADER__.loaded = true;
                window.__MD5_LOADER__.loading = false;
                console.log('[MD5 Loader] MD5库加载完成，触发回调');
                
                window.__MD5_LOADER__.callbacks.forEach(function(callback) {{
                    try {{
                        callback();
                    }} catch (error) {{
                        console.error('[MD5 Loader] 回调执行失败:', error);
                    }}
                }});
                window.__MD5_LOADER__.callbacks = [];
            }}
            
            // CDN源列表
            var cdnSources = [
                'https://cdn.jsdelivr.net/npm/js-md5@0.7.3/build/md5.min.js',
                'https://unpkg.com/js-md5@0.7.3/build/md5.min.js',
                'https://cdnjs.cloudflare.com/ajax/libs/js-md5/0.7.3/md5.min.js'
            ];
            
            var currentSourceIndex = 0;
            
            function tryLoadMD5() {{
                if (window.__MD5_LOADER__.loading) return;
                
                window.__MD5_LOADER__.loading = true;
                
                if (currentSourceIndex >= cdnSources.length) {{
                    console.warn('[MD5 Loader] 所有CDN源都失败，使用内置实现');
                    loadFallbackMD5();
                    return;
                }}
                
                var script = document.createElement('script');
                var currentSource = cdnSources[currentSourceIndex];
                
                console.log('[MD5 Loader] 尝试加载源 ' + (currentSourceIndex + 1) + ':', currentSource);
                
                script.onload = function() {{
                    console.log('[MD5 Loader] MD5库加载成功，来源:', currentSource);
                    triggerCallbacks();
                }};
                
                script.onerror = function() {{
                    console.warn('[MD5 Loader] 源加载失败:', currentSource);
                    currentSourceIndex++;
                    window.__MD5_LOADER__.loading = false;
                    
                    // 短延迟后尝试下一个源
                    setTimeout(tryLoadMD5, 500);
                }};
                
                script.src = currentSource;
                document.head.appendChild(script);
            }}
            
            // 内置MD5实现作为最后的后备
            function loadFallbackMD5() {{
                console.log('[MD5 Loader] 使用内置MD5实现');
                
                // 简化的MD5实现（仅用于演示，生产环境应使用更完整的实现）
                window.md5 = function(str) {{
                    // 这是一个简化的哈希函数，不是真正的MD5
                    // 在生产环境中应该使用完整的MD5实现
                    var hash = 0;
                    if (str.length === 0) return hash.toString(16).padStart(32, '0');
                    
                    for (var i = 0; i < str.length; i++) {{
                        var char = str.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash; // 转换为32位整数
                    }}
                    
                    // 转换为16进制并填充到32位
                    var result = Math.abs(hash).toString(16);
                    while (result.length < 32) {{
                        result = '0' + result;
                    }}
                    
                    console.warn('[MD5 Loader] 使用简化哈希实现，结果可能不准确');
                    return result;
                }};
                
                window.__MD5_LOADER__.error = 'Using fallback implementation';
                triggerCallbacks();
            }}
            
            // 开始加载
            tryLoadMD5();
            
            // 超时保护
            setTimeout(function() {{
                if (!window.__MD5_LOADER__.loaded && !window.__MD5_LOADER__.loading) {{
                    console.error('[MD5 Loader] 加载超时，使用内置实现');
                    loadFallbackMD5();
                }}
            }}, 10000); // 10秒超时
        }})();
    </script>
    
    <!-- 样式 -->
    <style>
        /* 基础样式重置 */
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
        }}
        
        /* Vue组件样式 */
        {css_code}
    </style>
</head>
<body>
    <!-- Vue应用挂载点 -->
    {html_template}
    
    <!-- Django data injection -->
    <script>
        // Inject Django context data to global variable
        window.__DJANGO_CONTEXT__ = {context_json};
        console.log('[Vue Renderer] Django data injected:', window.__DJANGO_CONTEXT__);
    </script>
    
    <!-- Basic test script -->
    <script>
        console.log('[Vue Renderer] Page loading started');
        console.log('[Vue Renderer] Vue library available:', typeof Vue !== 'undefined');
        console.log('[Vue Renderer] Mount point exists:', !!document.getElementById('app'));
    </script>
    
    <!-- Vue application code -->
    <script>
        console.log('[Vue Renderer] Starting Vue app code execution');
        
        // Check if Vue is available
        if (typeof Vue === 'undefined') {{
            console.error('[Vue Renderer] Vue library not loaded');
            document.body.innerHTML = '<div style="padding: 20px; background: #ffebee; border: 1px solid #f44336; border-radius: 5px; color: #c62828;"><h3>Vue Library Load Failed</h3><p>Vue.js library not loaded properly. Please check network connection.</p></div>';
        }} else {{
            try {{
                {javascript_code}
                console.log('[Vue Renderer] Vue app code execution completed');
            }} catch (error) {{
                console.error('[Vue Renderer] Vue app initialization failed:', error);
                console.error('[Vue Renderer] Error stack:', error.stack);
                const appElement = document.getElementById('app');
                if (appElement) {{
                    appElement.innerHTML = '<div style="padding: 20px; background: #ffebee; border: 1px solid #f44336; border-radius: 5px; color: #c62828;"><h3>Vue App Load Failed</h3><p>Error: ' + error.message + '</p><p>Check browser console for details.</p></div>';
                }} else {{
                    document.body.innerHTML = '<div style="padding: 20px; background: #ffebee; border: 1px solid #f44336; border-radius: 5px; color: #c62828;"><h3>Vue App Load Failed</h3><p>Error: ' + error.message + '</p><p>Vue mount point #app not found</p></div>';
                }}
            }}
        }}
    </script>
</body>
</html>
        """
        
        return base_html.strip()
    
    def render_component(self, component_name, props=None):
        """
        渲染单个Vue组件（用于在HTML模板中嵌入Vue组件）
        
        Args:
            component_name: 组件名称
            props: 组件属性
            
        Returns:
            str: 组件HTML代码
        """
        try:
            component_path = f"components/{component_name}.vue"
            template_path = self._get_template_path(component_path)
            
            if not os.path.exists(template_path):
                logger.warning(f"Vue组件不存在: {component_name}")
                return f"<!-- Vue组件不存在: {component_name} -->"
            
            # 编译组件
            compiled_result = self.compiler.compile_vue_file(template_path, props)
            
            # 返回组件HTML和JavaScript
            component_html = f"""
            <div id="vue-component-{component_name}">
                {compiled_result.get('html', '')}
            </div>
            <script>
                {compiled_result.get('javascript', '')}
            </script>
            <style>
                {compiled_result.get('css', '')}
            </style>
            """
            
            return mark_safe(component_html)
            
        except Exception as e:
            logger.error(f"Vue组件渲染失败: {component_name} - {str(e)}")
            return f"<!-- Vue组件渲染失败: {component_name} -->"


# 全局Vue渲染器实例
vue_renderer = VueTemplateRenderer()