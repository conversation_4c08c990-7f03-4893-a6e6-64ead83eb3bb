<template>
  <div id="app">
    <!-- 简化版Vue组件用于调试编译器问题 -->
    
    <!-- Logo区域 -->
    <div class="widget">
      <div class="widget-content themed-background-flat text-center logo-header">
        <div class="logo-avatar-container">
          <div class="logo-avatar">
            <img src="https://q.qlogo.cn/headimg_dl?dst_uin=107766441&spec=640&img_type=jpg" alt="头像" class="avatar-img">
          </div>
        </div>
      </div>

      <div class="text-center logo-title">
        <h2>
          <span class="brand-link">
            <b>依思商城 - 调试版本</b>
          </span>
        </h2>
        <p class="brand-subtitle">Vue编译器调试测试</p>
      </div>
    </div>

    <!-- 调试信息显示 -->
    <div class="debug-info">
      <h3>调试信息</h3>
      <p>组件状态: [[ componentStatus ]]</p>
      <p>当前时间: [[ currentTime ]]</p>
      <p>测试数据: [[ testMessage ]]</p>
      
      <button @click="updateMessage" class="btn btn-primary">更新消息</button>
      <button @click="testAsyncFunction" class="btn btn-success">测试异步函数</button>
    </div>

    <!-- 简单的Tab测试 -->
    <div class="simple-tabs">
      <div class="tab-buttons">
        <button @click="activeTab = 'tab1'" :class="{ active: activeTab === 'tab1' }">Tab 1</button>
        <button @click="activeTab = 'tab2'" :class="{ active: activeTab === 'tab2' }">Tab 2</button>
      </div>
      
      <div class="tab-content">
        <div v-show="activeTab === 'tab1'" class="tab-pane">
          <h4>Tab 1 内容</h4>
          <p>这是第一个标签页的内容</p>
        </div>
        <div v-show="activeTab === 'tab2'" class="tab-pane">
          <h4>Tab 2 内容</h4>
          <p>这是第二个标签页的内容</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'IndexDebug',
  data() {
    return {
      componentStatus: '初始化中...',
      currentTime: '',
      testMessage: '初始消息',
      activeTab: 'tab1'
    }
  },
  methods: {
    updateMessage() {
      this.testMessage = '消息已更新 - ' + new Date().toLocaleTimeString();
      console.log('消息已更新');
    },
    
    async testAsyncFunction() {
      console.log('开始测试异步函数');
      this.componentStatus = '异步函数执行中...';
      
      try {
        // 模拟异步操作
        await new Promise(resolve => setTimeout(resolve, 1000));
        this.componentStatus = '异步函数执行成功';
        console.log('异步函数执行成功');
      } catch (error) {
        this.componentStatus = '异步函数执行失败: ' + error.message;
        console.error('异步函数执行失败:', error);
      }
    },
    
    updateTime() {
      this.currentTime = new Date().toLocaleString();
    }
  },
  
  mounted() {
    console.log('IndexDebug组件已挂载');
    this.componentStatus = '组件已挂载';
    this.updateTime();
    
    // 每5秒更新一次时间
    setInterval(() => {
      this.updateTime();
    }, 5000);
  }
}
</script>

<style>
/* 基础样式 */
.widget {
  background-color: rgba(255, 255, 255, 0.9);
  margin-bottom: 12px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(233, 236, 239, 0.8);
}

.widget-content {
  padding: 12px;
}

.logo-header {
  background: #667eea;
  padding: 15px;
}

.logo-avatar {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  overflow: hidden;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50px;
}

.logo-title {
  padding: 12px;
  background: white;
}

.brand-link {
  color: #333;
  text-decoration: none;
  font-size: 18px;
}

.brand-subtitle {
  color: #6c757d;
  font-size: 12px;
  margin: 3px 0 0 0;
}

/* 调试信息样式 */
.debug-info {
  background: #f8f9fa;
  padding: 20px;
  margin: 20px 0;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.debug-info h3 {
  color: #495057;
  margin-bottom: 15px;
}

.debug-info p {
  margin: 8px 0;
  font-family: monospace;
  background: white;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  margin: 5px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn:hover {
  opacity: 0.9;
}

/* 简单Tab样式 */
.simple-tabs {
  margin: 20px 0;
}

.tab-buttons {
  display: flex;
  border-bottom: 1px solid #dee2e6;
}

.tab-buttons button {
  padding: 10px 20px;
  border: none;
  background: #f8f9fa;
  cursor: pointer;
  border-bottom: 2px solid transparent;
}

.tab-buttons button.active {
  background: white;
  border-bottom-color: #007bff;
  color: #007bff;
}

.tab-content {
  padding: 20px;
  background: white;
  border: 1px solid #dee2e6;
  border-top: none;
}

.tab-pane h4 {
  color: #495057;
  margin-bottom: 10px;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.themed-background-flat {
  background-color: #f9f9f9;
}
</style>
