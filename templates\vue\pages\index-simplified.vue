<template>
  <div id="app">
    <!-- 简化版原始组件，移除复杂异步逻辑 -->
    
    <!-- Logo区域 -->
    <div class="widget">
      <div class="widget-content themed-background-flat text-center logo-header">
        <div class="logo-avatar-container">
          <div class="logo-avatar">
            <img src="https://q.qlogo.cn/headimg_dl?dst_uin=107766441&spec=640&img_type=jpg" alt="头像" class="avatar-img">
          </div>
        </div>
      </div>

      <div class="text-center logo-title">
        <h2>
          <span class="brand-link">
            <b>依思商城 - 简化版</b>
          </span>
        </h2>
        <p class="brand-subtitle">Vue组件简化测试</p>
      </div>
    </div>

    <!-- 状态显示 -->
    <div class="status-info">
      <p>组件状态: [[ componentStatus ]]</p>
      <p>当前时间: [[ currentDateTime ]]</p>
      <p>MD5库状态: [[ md5LibLoaded ? '已加载' : '未加载' ]]</p>
    </div>

    <!-- Tab导航 -->
    <div class="tab-navigation">
      <ul class="nav nav-tabs">
        <li class="nav-item">
          <a class="nav-link" :class="{ active: activeTab === 'order' }" @click="switchTab('order')">下单</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" :class="{ active: activeTab === 'query' }" @click="switchTab('query')">查询</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" :class="{ active: activeTab === 'branch' }" @click="switchTab('branch')">分站</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" :class="{ active: activeTab === 'more' }" @click="switchTab('more')">更多</a>
        </li>
      </ul>
    </div>

    <!-- Tab内容 -->
    <div class="tab-content">
      <div v-show="activeTab === 'order'" class="tab-pane">
        <h4>下单页面</h4>
        <p>这里是下单功能</p>
        <button @click="testOrder" class="btn btn-primary">测试下单</button>
      </div>
      
      <div v-show="activeTab === 'query'" class="tab-pane">
        <h4>查询页面</h4>
        <p>这里是查询功能</p>
        <button @click="testQuery" class="btn btn-info">测试查询</button>
      </div>
      
      <div v-show="activeTab === 'branch'" class="tab-pane">
        <h4>分站页面</h4>
        <p>这里是分站功能</p>
        <button @click="testBranch" class="btn btn-success">测试分站</button>
      </div>
      
      <div v-show="activeTab === 'more'" class="tab-pane">
        <h4>更多页面</h4>
        <p>这里是更多功能</p>
        <button @click="testMore" class="btn btn-warning">测试更多</button>
      </div>
    </div>

    <!-- 测试结果显示 -->
    <div v-if="testResult" class="test-result">
      <h5>测试结果:</h5>
      <pre>[[ testResult ]]</pre>
    </div>
  </div>
</template>

<script>
export default {
  name: 'IndexSimplified',
  data() {
    return {
      // 基础状态
      componentStatus: '初始化中...',
      currentDateTime: '',
      activeTab: 'order',
      
      // MD5库状态
      md5LibLoaded: false,
      
      // 测试结果
      testResult: '',
      
      // 用户信息（简化）
      userInfo: null,
      
      // 分类数据（简化）
      categoriesData: []
    }
  },
  
  methods: {
    // 切换Tab
    switchTab(tab) {
      this.activeTab = tab;
      this.testResult = `切换到 ${tab} 标签页`;
      console.log('切换标签页:', tab);
    },
    
    // 测试下单功能
    testOrder() {
      this.testResult = '下单功能测试 - ' + new Date().toLocaleTimeString();
      console.log('测试下单功能');
    },
    
    // 测试查询功能
    testQuery() {
      this.testResult = '查询功能测试 - ' + new Date().toLocaleTimeString();
      console.log('测试查询功能');
    },
    
    // 测试分站功能
    testBranch() {
      this.testResult = '分站功能测试 - ' + new Date().toLocaleTimeString();
      console.log('测试分站功能');
    },
    
    // 测试更多功能
    testMore() {
      this.testResult = '更多功能测试 - ' + new Date().toLocaleTimeString();
      console.log('测试更多功能');
    },
    
    // 更新时间
    updateCurrentDateTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      
      this.currentDateTime = `［${year}年${month}月${day}日 ${hours}:${minutes}］当前系统时间`;
    },
    
    // 检查MD5库
    checkMD5Library() {
      if (typeof md5 !== 'undefined') {
        this.md5LibLoaded = true;
        console.log('MD5库检测成功');
        return true;
      } else {
        console.log('MD5库未加载');
        return false;
      }
    },
    
    // 简化的初始化
    initialize() {
      console.log('开始简化初始化...');
      
      // 更新时间
      this.updateCurrentDateTime();
      
      // 检查MD5库
      this.checkMD5Library();
      
      // 设置状态
      this.componentStatus = '初始化完成';
      
      console.log('简化初始化完成');
    }
  },
  
  mounted() {
    console.log('IndexSimplified组件已挂载');
    
    // 延迟初始化，确保所有依赖都已加载
    setTimeout(() => {
      this.initialize();
    }, 500);
    
    // 每分钟更新一次时间
    setInterval(() => {
      this.updateCurrentDateTime();
    }, 60000);
  }
}
</script>

<style>
/* 基础样式 */
.widget {
  background-color: rgba(255, 255, 255, 0.9);
  margin-bottom: 12px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(233, 236, 239, 0.8);
}

.widget-content {
  padding: 12px;
}

.logo-header {
  background: #667eea;
  padding: 15px;
}

.logo-avatar {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  overflow: hidden;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50px;
}

.logo-title {
  padding: 12px;
  background: white;
}

.brand-link {
  color: #333;
  text-decoration: none;
  font-size: 18px;
}

.brand-subtitle {
  color: #6c757d;
  font-size: 12px;
  margin: 3px 0 0 0;
}

/* 状态信息 */
.status-info {
  background: #f8f9fa;
  padding: 15px;
  margin: 15px 0;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.status-info p {
  margin: 5px 0;
  font-family: monospace;
}

/* Tab样式 */
.tab-navigation {
  margin: 20px 0;
}

.nav-tabs {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
  border-bottom: 1px solid #dee2e6;
}

.nav-item {
  margin-right: 2px;
}

.nav-link {
  display: block;
  padding: 10px 15px;
  text-decoration: none;
  color: #495057;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-bottom: none;
  cursor: pointer;
}

.nav-link.active {
  background: white;
  color: #007bff;
  border-bottom: 1px solid white;
  margin-bottom: -1px;
}

.nav-link:hover {
  background: #e9ecef;
}

.tab-content {
  background: white;
  padding: 20px;
  border: 1px solid #dee2e6;
  border-top: none;
}

.tab-pane h4 {
  color: #495057;
  margin-bottom: 15px;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  margin: 5px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary { background: #007bff; color: white; }
.btn-info { background: #17a2b8; color: white; }
.btn-success { background: #28a745; color: white; }
.btn-warning { background: #ffc107; color: #212529; }

.btn:hover { opacity: 0.9; }

/* 测试结果 */
.test-result {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  padding: 15px;
  margin: 15px 0;
  border-radius: 6px;
}

.test-result pre {
  margin: 0;
  font-family: monospace;
  white-space: pre-wrap;
}

/* 工具类 */
.text-center { text-align: center; }
.themed-background-flat { background-color: #f9f9f9; }
</style>
