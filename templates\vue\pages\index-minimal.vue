<template>
  <div id="app">
    <h1>最小化Vue组件测试</h1>
    <p>消息: [[ message ]]</p>
    <p>计数器: [[ counter ]]</p>
    <button @click="increment">点击增加</button>
    <button @click="updateMessage">更新消息</button>
  </div>
</template>

<script>
export default {
  name: 'IndexMinimal',
  data() {
    return {
      message: '初始消息',
      counter: 0
    }
  },
  methods: {
    increment() {
      this.counter++;
      console.log('计数器增加:', this.counter);
    },
    updateMessage() {
      this.message = '消息已更新 - ' + new Date().toLocaleTimeString();
      console.log('消息已更新');
    }
  },
  mounted() {
    console.log('最小化Vue组件已挂载');
    this.message = '组件已挂载';
  }
}
</script>

<style>
#app {
  padding: 20px;
  font-family: Arial, sans-serif;
}

h1 {
  color: #333;
}

button {
  margin: 5px;
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background: #0056b3;
}

p {
  margin: 10px 0;
  font-size: 16px;
}
</style>
